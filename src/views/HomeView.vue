<script setup lang="ts">
import { ref } from 'vue'

const products = ref([
  {
    title: 'Managed Services-WAN Connectivity',
    description: 'We offered Managed Services on WAN Connectivity from Fiber/Wireless up to SD WAN with ONE-STOP-Managed Services',
    img: 'https://i.imgur.com/Md2Eoej.png',
    link: '/products/wan-connectivity'
  },
  {
    title: 'Managed Services-Secure Gateway',
    description: 'Worry about cyber-attacks? No Gateway Firewall in place? No idea how to manage your gateway firewall?',
    img: 'https://i.imgur.com/8Hy1LWW.png',
    link: '/products/secure-gateway'
  },
  {
    title: 'Managed Services-Cloud',
    description: 'Looking for Be-spoke Cloud Solution? With OnXus, we can either move you to cloud or move cloud to you',
    img: 'https://i.imgur.com/PzzUd2O.png',
    link: '/products/cloud'
  },
  {
    title: 'Managed Services Portal',
    description: 'Central Hub to consolidate all the information and analytics into single managed services portal',
    img: 'https://i.imgur.com/wRuyCAi.png',
    link: '/products/managed-services'
  }
])

const certificates = ref([
  { name: 'ISO 27001', logo: '/certs/iso27001.png' },
  { name: 'ISO 9001', logo: '/certs/iso9001.png' },
  { name: 'MSC Status', logo: '/certs/msc.png' },
  { name: 'SIRIM', logo: '/certs/sirim.png' }
])
</script>

<template>
  <div>
    <!-- Hero Section -->
    <section class="h-[400px]">
      <div class="w-full h-full shadow-xl">
        <img src="/public/hero.jpeg" alt="Network Infrastructure" class="h-full" />
      </div>
    </section>

    <!-- Certificates Section -->
    <section class="py-16">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-left mb-12">
          <h2 class="text-lg font-bold text-gray-900 mb-4">License & Certificate:</h2>
        </div>
        <div>
          <img src="https://i.imgur.com/DbjjtYR.png" alt="Certificates" />
        </div>
      </div>
    </section>

    <!-- Products & Services Cards -->
    <section class="py-20 bg-white">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-2 md:grid-cols-4 gap-5">
          <div v-for="product in products" :key="product.title" class="group cursor-pointer" @click="$router.push(product.link)">
            <div class="mb-6">
              <img :src="product.img" alt="Product Image" />
            </div>
            <h3 class="text-xl font-bold text-gray-900 mb-4 group-hover:text-primary-600 transition-colors">{{ product.title }}</h3>
            <p class="text-gray-600 mb-6">{{ product.description }}</p>
            <div class="text-gray-900 text-sm">
              Find out more > <span class="text-green-400 hover:text-green-500"> Click Our Products & Services </span>
            </div>
          </div>
        </div>
      </div>
    </section>

    <section>
      <div class="bg-black">
        <div class="h-[400px] relative w-full bg-cover bg-center bg-no-repeat grid grid-cols-1 md:grid-cols-2 p-32 md:px-20" style="background-image: linear-gradient(rgba(0,0,0,0.5), rgba(0,0,0,0.5)), url(https://i.imgur.com/F1Ut2MY.png);">
          <div class="absolute z-10 top-16 left-10 text-5xl font-bold text-white">
            BUILDING<br>BEYOND<br>NETWORK
            <div style="top:210px;" class="text-xl z-10 left-10 text-white">Ready to dive in?</div>
            <div style="top:235px;" class="text-lg z-10 left-10 text-emerald-400 g1">Start your managed services today</div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>
