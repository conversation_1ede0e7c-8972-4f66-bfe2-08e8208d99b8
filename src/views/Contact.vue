<template>
  <div class="min-h-screen bg-white">
    <!-- Hero Section -->
    <section class="bg-white text-gray-900">
      <div class="">
        <img src="https://i.imgur.com/V60sgW2.jpg" class="w-full h-[264px] overflow-hidden object-cover" style="object-position: 100% 65%;">
      </div>
      <div class="px-10 py-[20px] text-white bg-gradient-to-tr  from-green-700 to-cyan-500">
          <h1 class="text-3xl lg:text-4xl font-bold">Contact Us</h1>
      </div>
    </section>

    <!-- Map & Contact Information -->
    <section class="py-16">
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-1">
        <!-- Map -->
        <div class="p-10">
          <iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3984.1102092070323!2d101.60730171078698!3d3.0652017968976475!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x31cc4d8acccda7af%3A0xae78833ccbee5013!2sJenexus%20Holding%20Sdn%20Bhd!5e0!3m2!1sms!2smy!4v1757382509736!5m2!1sms!2smy" width="600" height="450" style="border:0;" allowfullscreen="" loading="lazy" referrerpolicy="no-referrer-when-downgrade"></iframe>
        </div>

        <!-- Contact Information -->
        <div class="p-10 text-gray-900">
          <b>Sales Enquiry</b> <br>
            •	Email to: <EMAIL><br><br>
            <b>Billing Enquiry</b><br>
            •	Email to: <EMAIL><br>
            •	Contact: +603-5612 7577<br><br>
            <b>	Technical Support </b><br>
            •	Hotline: 1700 81 6488 <br>
            •	Email to: <EMAIL><br><br>
            <b>	Career / Job Enquiry</b><br>
            •	Email to: <EMAIL><br>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const form = ref({
  firstName: '',
  lastName: '',
  email: '',
  company: '',
  phone: '',
  service: '',
  message: ''
})

const submitForm = () => {
  // Handle form submission
  console.log('Form submitted:', form.value)
  alert('Thank you for your message! We will get back to you soon.')
  
  // Reset form
  form.value = {
    firstName: '',
    lastName: '',
    email: '',
    company: '',
    phone: '',
    service: '',
    message: ''
  }
}
</script>
