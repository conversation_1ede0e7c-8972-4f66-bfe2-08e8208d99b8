import { createRouter, createWebHistory } from 'vue-router'
import HomeView from '../views/HomeView.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: HomeView,
    },
    // About Us Routes
    {
      path: '/about/company-overview',
      name: 'company-overview',
      component: () => import('../views/about/CompanyOverview.vue'),
    },
    {
      path: '/about/compliance-certifications',
      name: 'compliance-certifications',
      component: () => import('../views/about/ComplianceCertifications.vue'),
    },
    {
      path: '/about/affiliates-partners',
      name: 'affiliates-partners',
      component: () => import('../views/about/AffiliatesPartners.vue'),
    },
    {
      path: '/about/our-client',
      name: 'our-client',
      component: () => import('../views/about/OurClient.vue'),
    },
    {
      path: '/about/qms-quality',
      name: 'qms-quality',
      component: () => import('../views/about/QMSQuality.vue'),
    },
    // Products & Services Routes
    {
      path: '/products/wan-connectivity',
      name: 'wan-connectivity',
      component: () => import('../views/products/WANConnectivity.vue'),
    },
    {
      path: '/products/secure-gateway',
      name: 'secure-gateway',
      component: () => import('../views/products/SecureGateway.vue'),
    },
    {
      path: '/products/cloud',
      name: 'cloud',
      component: () => import('../views/products/Cloud.vue'),
    },
    {
      path: '/products/managed-services',
      name: 'managed-services',
      component: () => import('../views/products/ManagedServices.vue'),
    },
    // Other Routes
    {
      path: '/data-centre',
      name: 'data-centre',
      component: () => import('../views/DataCentre.vue'),
    },
    {
      path: '/news-events',
      name: 'news-events',
      component: () => import('../views/NewsEvents.vue'),
    },
    {
      path: '/contact',
      name: 'contact',
      component: () => import('../views/Contact.vue'),
    },
    // Legal Pages
    {
      path: '/terms-of-service',
      name: 'terms-of-service',
      component: () => import('../views/legal/TermsOfService.vue'),
    },
    {
      path: '/cookies',
      name: 'cookies',
      component: () => import('../views/legal/Cookies.vue'),
    },
    {
      path: '/privacy-policy',
      name: 'privacy-policy',
      component: () => import('../views/legal/PrivacyPolicy.vue'),
    },
  ],
})

export default router
